# -*- coding: utf-8 -*-
"""
Reference Image Plane widget for accessibility features.
"""
# Import built-in modules
import logging

# Import third-party modules
import dayu_widgets
from qtpy import QtCore
from qtpy import QtWidgets

# Import local modules
from cgame_avatar_factory.common import constants as const
from cgame_avatar_factory.face_sculpting_center.accessibility import ref_image_plane_utils

# Module constants
ANGLE_SLIDER_RANGE = (-150, 150)  # 0.001 deg step with int slider
ANGLE_SLIDER_PAGE_STEP = 5  # 0.5 deg per page
ANGLE_SLIDER_SINGLE_STEP = 1  # 0.001 deg per tick
SLIDER_LABEL_WIDTH = 70

# Property slider scale (non-rotation): 0.001 precision
PROP_SLIDER_SCALE = 1000


class RefImagePlaneWidget(QtWidgets.QFrame):
    """Widget for reference image plane controls.

    Provides view switching buttons and image property controls including
    size, opacity, horizontal/vertical offset, and distance.
    """

    def __init__(self, parent=None):
        super(RefImagePlaneWidget, self).__init__(parent)
        self.logger = logging.getLogger(__name__)

        self.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.main_layout = QtWidgets.QVBoxLayout()
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.main_layout.setSpacing(10)
        self.setLayout(self.main_layout)

        # Ensure the widget can expand
        self.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        self.setMinimumHeight(150)  # Reasonable minimum height

        # Store reference to right panel for enabling/disabling
        self.right_panel = None

        # Track current view
        self.current_view = None

        # Store mapping between view names and buttons - initialize early
        self.view_button_mapping = {}

        self.setup_ui()

        # Set up timer to check view changes
        self.view_check_timer = QtCore.QTimer()
        self.view_check_timer.timeout.connect(self.check_view_change)
        self.view_check_timer.start(500)  # Check every 500ms

        # Initialize button backgrounds after UI setup
        QtCore.QTimer.singleShot(100, self.initialize_button_backgrounds)

    def setup_ui(self):
        """Setup the user interface."""
        # Create main horizontal layout
        main_h_layout = QtWidgets.QHBoxLayout()
        main_h_layout.setContentsMargins(0, 0, 0, 0)
        main_h_layout.setSpacing(10)
        self.main_layout.addLayout(main_h_layout)

        # Left side - View buttons and load image (50% width)
        self.setup_left_panel(main_h_layout)

        # Add vertical separator line
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.VLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        separator.setLineWidth(5)
        main_h_layout.addWidget(separator)

        # Right side - Property controls (50% width)
        self.setup_right_panel(main_h_layout)

    def setup_left_panel(self, parent_layout):
        """Setup the left panel with view buttons."""
        left_panel = QtWidgets.QFrame()
        left_layout = QtWidgets.QVBoxLayout()
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(0)
        left_panel.setLayout(left_layout)
        left_panel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        # View buttons container - single row with all 5 buttons
        view_buttons_container = QtWidgets.QFrame()
        view_buttons_layout = QtWidgets.QHBoxLayout()
        view_buttons_layout.setContentsMargins(0, 0, 0, 0)
        view_buttons_layout.setSpacing(15)
        view_buttons_container.setLayout(view_buttons_layout)
        view_buttons_container.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        # Create view buttons - 5 buttons in single row
        view_button_configs = [
            {"text": "透视", "icon": "🧊", "callback": self.on_cube_button_clicked},
            {"text": "正", "icon": None, "callback": lambda: self.on_view_button_clicked("正")},
            {"text": "30°", "icon": None, "callback": lambda: self.on_view_button_clicked("30°")},
            {"text": "40°", "icon": None, "callback": lambda: self.on_view_button_clicked("40°")},
            {"text": "右", "icon": None, "callback": lambda: self.on_view_button_clicked("右")},
        ]

        self.view_buttons = []

        for config in view_button_configs:
            btn = QtWidgets.QPushButton(config["icon"] if config["icon"] else config["text"])
            btn.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

            # Apply button styling
            if config["icon"]:  # Perspective button with icon
                btn.setStyleSheet(
                    f"""
                    QPushButton {{
                        border: 2px solid {const.BUTTON_BORDER};
                        border-radius: 8px;
                        background-color: {const.BUTTON_BG};
                        color: {const.DAYU_PRIMARY_TEXT_COLOR};
                        font-weight: bold;
                        font-size: 100px;
                        text-align: center;
                    }}
                    QPushButton:hover {{
                        border-color: {const.BUTTON_HOVER_BORDER};
                        background-color: {const.BUTTON_HOVER_BG};
                    }}
                    QPushButton:pressed {{
                        background-color: {const.BUTTON_PRESSED_BG};
                    }}
                    """
                )
            else:  # Regular view buttons
                btn.setStyleSheet(
                    f"""
                    QPushButton {{
                        border: 2px solid {const.BUTTON_BORDER};
                        border-radius: 8px;
                        background-color: {const.BUTTON_BG};
                        color: rgba(200, 200, 200, 77);
                        font-weight: bold;
                        font-size: 100px;
                        text-align: center;
                    }}
                    QPushButton:hover {{
                        border-color: {const.BUTTON_HOVER_BORDER};
                        background-color: {const.BUTTON_HOVER_BG};
                        color: rgba(200, 200, 200, 255);
                    }}
                    QPushButton:pressed {{
                        background-color: {const.BUTTON_PRESSED_BG};
                        color: rgba(200, 200, 200, 255);
                    }}
                    """
                )

            btn.clicked.connect(config["callback"])

            # Add right-click context menu for view buttons (not perspective button)
            if not config["icon"]:  # Only for view buttons, not perspective button
                btn.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
                btn.customContextMenuRequested.connect(
                    lambda pos, view=config["text"], button=btn: self.show_context_menu(button, pos, view),
                )
                # Store view name for context menu
                btn.view_name = config["text"]
                # Store button mapping for background image setting
                self.view_button_mapping[config["text"]] = btn

            self.view_buttons.append(btn)
            view_buttons_layout.addWidget(btn)

        left_layout.addWidget(view_buttons_container, 1)  # Fill the entire left panel

        parent_layout.addWidget(left_panel, 3)  # 3/5 of total width

    def setup_right_panel(self, parent_layout):
        """Setup the right panel with property controls."""
        self.right_panel = QtWidgets.QFrame()

        # Add border and background style to the right panel
        '''self.right_panel.setStyleSheet(
            f"""
            QFrame {{
                border: 1px solid {const.BUTTON_HOVER_BG};
                border-radius: 2px;
                background-color: {const.UI_COLOR_BG_DARKER};
                padding: 2px;
            }}
            """
        )'''

        right_layout = QtWidgets.QVBoxLayout()
        right_layout.setContentsMargins(10, 10, 10, 10)
        right_layout.setSpacing(10)
        self.right_panel.setLayout(right_layout)
        self.right_panel.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

        # Property controls
        properties = [
            {"label": "不透明度", "min": 0.0, "max": 1.0, "default": 0.5},
            {"label": "大小", "min": 0.1, "max": 3.0, "default": 1.0},
            {"label": "水平偏移", "min": -0.5, "max": 0.5, "default": 0.0},
            {"label": "垂直偏移", "min": -0.5, "max": 0.5, "default": 0.0},
            {"label": "旋转", "min": -180.0, "max": 180.0, "default": 0.0},
        ]

        self.property_controls = {}

        for prop in properties:
            # Create property row
            prop_container = QtWidgets.QFrame()
            prop_layout = QtWidgets.QHBoxLayout()
            prop_layout.setContentsMargins(0, 0, 0, 0)
            prop_layout.setSpacing(8)
            prop_container.setLayout(prop_layout)

            # Label (fixed width, left-aligned, bold font)
            label = dayu_widgets.MLabel(prop["label"] + "：")
            label.setFixedWidth(100)
            label.setAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)

            # Set bold font and border using stylesheet
            label.setStyleSheet(
                """
                QLabel {
                    font-weight: bold;
                    border: 1px solid #555555;
                    border-radius: 3px;
                }
            """
            )

            prop_layout.addWidget(label)

            # Input field container (LineEdit + Reset button) - fixed width
            input_container = QtWidgets.QWidget()
            input_container.setFixedWidth(
                125,
            )  # Fixed width for input area (80px input + 38px button + 5px spacing + 2px margin)
            input_container_layout = QtWidgets.QHBoxLayout()
            input_container_layout.setContentsMargins(0, 0, 0, 0)
            input_container_layout.setSpacing(2)
            input_container.setLayout(input_container_layout)

            # Input field (LineEdit style)
            input_field = dayu_widgets.MLineEdit()
            input_field.setText(str(prop["default"]))
            input_field.setFixedWidth(80)

            # Add validator for numeric input
            # Import third-party modules
            from qtpy import QtGui

            if prop["label"] == "旋转":
                # Integer validator for rotation
                validator = QtGui.QIntValidator(int(prop["min"]), int(prop["max"]))
            else:
                # Double validator for other properties
                validator = QtGui.QDoubleValidator(prop["min"], prop["max"], 2)
            input_field.setValidator(validator)

            # Connect text change to value change
            input_field.textChanged.connect(
                lambda text, prop_name=prop["label"]: self.on_input_text_changed(prop_name, text),
            )
            input_container_layout.addWidget(input_field)

            input_container_layout.setSpacing(5)

            # Reset button with SVG icon - following parametric_catchlight_widget pattern
            reset_button = dayu_widgets.MToolButton().icon_only().svg("reset.svg")
            reset_button.setFixedSize(38, 24)
            reset_button.setIconSize(QtCore.QSize(16, 16))
            reset_button.setToolTip(f"重置{prop['label']}到默认值")
            reset_button.setStyleSheet(
                f"""
                MToolButton {{
                    background-color: transparent;
                    border: none;
                }}
                MToolButton:hover {{
                    background-color: {const.BUTTON_HOVER_BG};
                    border-radius: 3px;
                }}
                MToolButton:pressed {{
                    background-color: {const.UI_COLOR_BG_DARKER};
                }}
                """
            )

            # Create a proper connection function to avoid lambda issues
            def create_reset_handler(prop_name, default_val, field):
                def reset_handler():
                    self.on_reset_property(prop_name, default_val, field)

                return reset_handler

            reset_button.clicked.connect(create_reset_handler(prop["label"], prop["default"], input_field))
            input_container_layout.addWidget(reset_button)

            prop_layout.addWidget(input_container)

            # Slider - takes up remaining space
            slider = dayu_widgets.MSlider(QtCore.Qt.Horizontal)
            # Set range and default value based on property type
            if self._is_rotation_property(prop["label"]):
                slider.setRange(int(prop["min"]), int(prop["max"]))
                slider.setValue(int(prop["default"]))
            else:
                slider.setRange(int(prop["min"] * PROP_SLIDER_SCALE), int(prop["max"] * PROP_SLIDER_SCALE))
                slider.setValue(int(prop["default"] * PROP_SLIDER_SCALE))
            slider.valueChanged.connect(
                lambda value, prop_name=prop["label"], field=input_field: self.on_slider_changed(
                    prop_name,
                    value,
                    field,
                ),
            )

            # Set different colors for different sliders
            self._set_slider_color(slider, prop["label"])

            # Let slider expand to fill remaining space
            prop_layout.addWidget(slider, 1)  # stretch factor = 1

            # Store references
            self.property_controls[prop["label"]] = {
                "input": input_field,
                "slider": slider,
                "min": prop["min"],
                "max": prop["max"],
            }

            right_layout.addWidget(prop_container)

        right_layout.addStretch()
        parent_layout.addWidget(self.right_panel, 2)  # 2/5 of total width

    def _is_rotation_property(self, property_name):
        """Check if property is rotation (needs special handling).

        检查属性是否为旋转（需要特殊处理）。

        Args:
            property_name (str): Property name

        Returns:
            bool: True if rotation property
        """
        return property_name == "旋转"

    def _convert_value_to_slider(self, property_name, value):
        """Convert property value to slider value.

        将属性值转换为滑杆值。

        Args:
            property_name (str): Property name
            value (float): Property value

        Returns:
            int: Slider value
        """
        return int(value) if self._is_rotation_property(property_name) else int(value * PROP_SLIDER_SCALE)

    def _convert_slider_to_value(self, property_name, slider_value):
        """Convert slider value to property value.

        将滑杆值转换为属性值。

        Args:
            property_name (str): Property name
            slider_value (int): Slider value

        Returns:
            float: Property value
        """
        return (
            float(slider_value)
            if self._is_rotation_property(property_name)
            else slider_value / float(PROP_SLIDER_SCALE)
        )

    def _set_slider_color(self, slider, property_name):
        """Set different colors for different sliders.

        为不同的滑杆设置不同的颜色。

        Args:
            slider: MSlider widget
            property_name (str): Property name
        """
        # Define color mapping for different properties
        color_mapping = {
            "不透明度": "#21F3C4",
            "大小": "#91E773",
            "水平偏移": "#2196F3",
            "垂直偏移": "#2196F3",
            "旋转": "#C951EC",
        }

        color = color_mapping.get(property_name, "#2196F3")  # Default to blue

        # Set slider color using stylesheet
        slider.setStyleSheet(
            f"""
            QSlider::groove:horizontal {{
                border: 1px solid #999999;
                height: 6px;
                background: #333333;
                border-radius: 3px;
            }}
            QSlider::handle:horizontal {{
                background: {color};
                border: 2px solid {color};
                width: 16px;
                height: 16px;
                border-radius: 8px;
                margin: -5px 0;
            }}
            QSlider::handle:horizontal:hover {{
                background: {color};
                border: 2px solid #ffffff;
            }}
            QSlider::handle:horizontal:pressed {{
                background: {color};
                border: 2px solid #ffffff;
            }}
            QSlider::sub-page:horizontal {{
                background: {color};
                border: 1px solid #999999;
                height: 6px;
                border-radius: 3px;
            }}
            QSlider::add-page:horizontal {{
                background: #555555;
                border: 1px solid #999999;
                height: 6px;
                border-radius: 3px;
            }}
        """
        )

    def on_view_button_clicked(self, view_name):
        """Handle view button click events."""
        try:
            # Switch to the corresponding Maya view
            ref_image_plane_utils.switch_view_by_name(view_name)
            # Update UI state immediately after view switch
            QtCore.QTimer.singleShot(100, self.update_ui_state)
        except Exception as e:
            self.logger.error(f"Failed to switch to {view_name} view: {e}")

    def on_cube_button_clicked(self):
        """Handle cube button click events."""
        try:
            # Switch to perspective view using the same mechanism as other views
            ref_image_plane_utils.switch_view_by_name("透视")
            # Update UI state immediately after view switch
            QtCore.QTimer.singleShot(100, self.update_ui_state)
        except Exception as e:
            self.logger.error(f"Failed to switch to perspective view: {e}")

    def check_view_change(self):
        """Check if the current view has changed and update UI accordingly."""
        try:
            current_view = ref_image_plane_utils.get_current_view_name()
            if current_view != self.current_view:
                self.current_view = current_view
                self.update_ui_state()
        except Exception as e:
            self.logger.warning(f"Failed to check view change: {e}")

    def update_ui_state(self):
        """Update UI state based on current view."""
        try:
            is_perspective = ref_image_plane_utils.is_perspective_view()

            # Enable/disable right panel based on view
            if self.right_panel:
                self.right_panel.setEnabled(not is_perspective)

            # Update slider values based on current view's image plane properties
            if not is_perspective:
                self.sync_slider_values()

        except Exception as e:
            self.logger.error(f"Failed to update UI state: {e}")

    def sync_slider_values(self):
        """Synchronize slider values with current view's image plane properties.

        同步滑杆数值与当前视图的image plane属性。
        """
        try:
            # Get current view name
            current_view = ref_image_plane_utils.get_current_view_name()
            if not current_view or current_view == "透视":
                return

            # Get all image plane properties for current view
            properties = ref_image_plane_utils.get_all_image_plane_properties(current_view)

            # Update each control with the retrieved values
            for prop_name, value in properties.items():
                if prop_name in self.property_controls:
                    control = self.property_controls[prop_name]

                    # Block signals to prevent triggering change events
                    control["input"].blockSignals(True)
                    control["slider"].blockSignals(True)

                    # Update input field
                    control["input"].setText(str(value))

                    # Update slider
                    slider_value = self._convert_value_to_slider(prop_name, value)
                    control["slider"].setValue(slider_value)

                    # Re-enable signals
                    control["input"].blockSignals(False)
                    control["slider"].blockSignals(False)

        except Exception as e:
            self.logger.error(f"Failed to sync slider values: {e}")

    def initialize_button_backgrounds(self):
        """Initialize button backgrounds based on existing image planes.

        根据现有的image plane初始化按钮背景。
        """
        try:
            # Ensure mapping has been populated by setup_left_panel
            if not self.view_button_mapping:
                self.logger.warning("view_button_mapping not ready, skipping background initialization")
                return

            for view_name in ref_image_plane_utils.ORTHOGRAPHIC_VIEWS:
                # Check if this view has an image plane
                image_path = ref_image_plane_utils.get_image_plane_image_path(view_name)
                if image_path:
                    # Set button background if image plane exists
                    self.set_button_background_image(view_name, image_path)

        except Exception as e:
            self.logger.error(f"Failed to initialize button backgrounds: {e}")

    def set_button_background_image(self, view_name, image_path):
        """Set background image for the specified view button.

        为指定视图按钮设置背景图片。

        Args:
            view_name (str): View name ("正", "30°", "40°", "右")
            image_path (str): Path to the image file
        """
        try:
            if view_name not in self.view_button_mapping:
                self.logger.warning(f"No button found for view {view_name}")
                return

            button = self.view_button_mapping[view_name]

            # Convert path to use forward slashes for Qt
            qt_image_path = image_path.replace("\\", "/")

            # Set button style with background image
            button.setStyleSheet(
                f"""
                QPushButton {{
                    border: 2px solid {const.BUTTON_BORDER};
                    border-radius: 8px;
                    background-image: url({qt_image_path});
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: cover;
                    background-color: {const.BUTTON_BG};
                    color: rgba(255, 255, 255, 200);
                    font-weight: bold;
                    font-size: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                }}
                QPushButton:hover {{
                    border-color: {const.BUTTON_HOVER_BORDER};
                    background-color: {const.BUTTON_HOVER_BG};
                    color: rgba(255, 255, 255, 255);
                }}
                QPushButton:pressed {{
                    border-color: {const.BUTTON_PRESSED_BG};
                    background-color: {const.BUTTON_PRESSED_BG};
                    color: rgba(255, 255, 255, 255);
                }}
                """
            )

        except Exception as e:
            self.logger.error(f"Failed to set background image for {view_name}: {e}")

    def reset_button_style(self, view_name):
        """Reset button style to default for the specified view.

        重置指定视图按钮的样式为默认样式。

        Args:
            view_name (str): View name ("正", "30°", "40°", "右")
        """
        try:
            if view_name not in self.view_button_mapping:
                self.logger.warning(f"No button found for view {view_name}")
                return

            button = self.view_button_mapping[view_name]

            # Reset to default style
            button.setStyleSheet(
                f"""
                QPushButton {{
                    border: 2px solid {const.BUTTON_BORDER};
                    border-radius: 8px;
                    background-color: {const.BUTTON_BG};
                    color: rgba(200, 200, 200, 77);
                    font-weight: bold;
                    font-size: 100px;
                    text-align: center;
                }}
                QPushButton:hover {{
                    border-color: {const.BUTTON_HOVER_BORDER};
                    background-color: {const.BUTTON_HOVER_BG};
                    color: rgba(200, 200, 200, 255);
                }}
                QPushButton:pressed {{
                    background-color: {const.BUTTON_PRESSED_BG};
                    color: rgba(200, 200, 200, 255);
                }}
                """
            )

        except Exception as e:
            self.logger.error(f"Failed to reset button style for {view_name}: {e}")

    def on_input_text_changed(self, property_name, text):
        """Handle input field text changes.

        处理输入框文本变化。

        Args:
            property_name (str): Property name
            text (str): Input text
        """
        try:
            # Convert text to float/int based on property type
            if self._is_rotation_property(property_name):
                value = int(text) if text else 0
            else:
                value = float(text) if text else 0.0

            # Update corresponding slider
            if property_name in self.property_controls:
                control = self.property_controls[property_name]
                slider_value = self._convert_value_to_slider(property_name, value)
                control["slider"].blockSignals(True)
                control["slider"].setValue(slider_value)
                control["slider"].blockSignals(False)

            # Apply property change to image plane
            self.apply_property_change(property_name, value)

        except ValueError:
            # Invalid input, ignore
            pass
        except Exception as e:
            self.logger.warning(f"Error handling input text change for {property_name}: {e}")

    def on_reset_property(self, property_name, default_value, input_field):
        """Handle property reset to default value.

        处理属性重置到默认值。

        Args:
            property_name (str): Property name
            default_value (float): Default value
            input_field: Input field widget
        """
        try:
            # Set input field to default value
            input_field.setText(str(default_value))

            # Update corresponding slider
            if property_name in self.property_controls:
                control = self.property_controls[property_name]
                slider_value = self._convert_value_to_slider(property_name, default_value)
                control["slider"].blockSignals(True)
                control["slider"].setValue(slider_value)
                control["slider"].blockSignals(False)
            else:
                self.logger.warning(f"Property {property_name} not found in property_controls")

            # Apply property change to image plane
            self.apply_property_change(property_name, default_value)

        except Exception as e:
            self.logger.error(f"Error resetting property {property_name}: {e}")
            # Import built-in modules
            import traceback

            traceback.print_exc()

    def on_slider_changed(self, property_name, slider_value, input_field):
        """Handle slider value changes."""
        value = self._convert_slider_to_value(property_name, slider_value)
        input_field.blockSignals(True)
        input_field.setText(str(value))
        input_field.blockSignals(False)

        # Apply property change to image plane
        self.apply_property_change(property_name, value)

    def apply_property_change(self, property_name, value):
        """Apply property change to the current view's image plane.

        将属性更改应用到当前视图的image plane。

        Args:
            property_name (str): Property name ("大小", "不透明度", "水平偏移", "垂直偏移", "旋转")
            value (float): Property value
        """
        try:
            # Get current view name and check if it's valid for image plane operations
            current_view = ref_image_plane_utils.get_current_view_name()
            if not current_view or current_view == "透视":
                return

            # Apply the property change
            ref_image_plane_utils.set_image_plane_property(current_view, property_name, value)

        except Exception as e:
            self.logger.error(f"Error applying property change {property_name} = {value}: {e}")

    def show_context_menu(self, button, pos, view_name):
        """Show context menu for view buttons.

        为视图按钮显示右键菜单。

        Args:
            button: The button that was right-clicked
            pos: Position where the menu should appear
            view_name: Name of the view ("正", "30°", "40°", "右")
        """
        try:
            # Reset button state to avoid stuck hover effect
            button.setAttribute(QtCore.Qt.WA_UnderMouse, False)
            button.update()

            # Create context menu
            context_menu = QtWidgets.QMenu(self)

            # Import image action - using direct connection for consistency
            import_action = QtWidgets.QAction("导入图片", self)

            # Create a direct function to match delete handler
            def import_handler():
                self.import_image_for_view(view_name)

            import_action.triggered.connect(import_handler)
            context_menu.addAction(import_action)

            # Delete image action - using direct connection
            delete_action = QtWidgets.QAction("删除图片", self)

            # Create a direct function to avoid lambda issues
            def delete_handler():
                self.delete_image_for_view(view_name)

            delete_action.triggered.connect(delete_handler)
            context_menu.addAction(delete_action)

            # Temporarily disable the image plane check to avoid blocking
            # The check will be done inside the delete method instead
            delete_action.setToolTip(f"删除{view_name}视图的图片平面")

            # Add separator
            context_menu.addSeparator()

            # Align model action
            align_action = QtWidgets.QAction("对齐模型", self)

            def align_handler():
                self.align_model_for_view(view_name)

            align_action.triggered.connect(align_handler)
            context_menu.addAction(align_action)
            align_action.setToolTip(f"手动对齐相机到{view_name}视图的模型")

            # Extra sliders for ortho views (正/30°/40°/右)
            if view_name in ref_image_plane_utils.ORTHOGRAPHIC_VIEWS:
                context_menu.addSeparator()

                # "水平旋转" slider in menu
                h_widget = QtWidgets.QWidget(self)
                h_layout = QtWidgets.QHBoxLayout(h_widget)
                h_layout.setContentsMargins(8, 4, 8, 4)
                h_layout.setSpacing(6)

                h_label = dayu_widgets.MLabel("水平旋转")
                h_label.setFixedWidth(SLIDER_LABEL_WIDTH)
                h_label.setToolTip("以头部中心绕上轴旋转 (-15~15°)")

                h_slider = dayu_widgets.MSlider(QtCore.Qt.Horizontal)
                h_slider.setRange(*ANGLE_SLIDER_RANGE)  # 0.001 deg step with int slider
                h_slider.setValue(0)
                h_slider.setPageStep(ANGLE_SLIDER_PAGE_STEP)  # 0.5 deg per page
                h_slider.setSingleStep(ANGLE_SLIDER_SINGLE_STEP)  # 0.001 deg per tick
                # valueChanged emits int; map to 0.001° double and pass view name string
                h_slider.valueChanged.connect(
                    lambda v, vn=str(view_name): self.on_menu_horizontal_rotate_changed(vn, v / 10.0)
                )

                h_layout.addWidget(h_label)
                h_layout.addWidget(h_slider)

                h_action = QtWidgets.QWidgetAction(self)
                h_action.setDefaultWidget(h_widget)
                context_menu.addAction(h_action)

                # "上下旋转" slider in menu
                v_widget = QtWidgets.QWidget(self)
                v_layout = QtWidgets.QHBoxLayout(v_widget)
                v_layout.setContentsMargins(8, 4, 8, 4)
                v_layout.setSpacing(6)

                v_label = dayu_widgets.MLabel("上下旋转")
                v_label.setFixedWidth(SLIDER_LABEL_WIDTH)
                v_label.setToolTip("以头部中心绕相机右轴旋转 (-15~15°)")

                v_slider = dayu_widgets.MSlider(QtCore.Qt.Horizontal)
                v_slider.setRange(*ANGLE_SLIDER_RANGE)  # 0.001 deg step with int slider
                v_slider.setValue(0)
                v_slider.setPageStep(ANGLE_SLIDER_PAGE_STEP)  # 0.5 deg per page
                v_slider.setSingleStep(ANGLE_SLIDER_SINGLE_STEP)  # 0.001 deg per tick
                # valueChanged emits int; map to 0.001° double and pass view name string
                v_slider.valueChanged.connect(
                    lambda v, vn=str(view_name): self.on_menu_vertical_rotate_changed(vn, v / 10.0)
                )

                v_layout.addWidget(v_label)
                v_layout.addWidget(v_slider)

                v_action = QtWidgets.QWidgetAction(self)
                v_action.setDefaultWidget(v_widget)
                context_menu.addAction(v_action)

                # Reset view action for ortho views
                reset_action = QtWidgets.QAction("重置视角", self)
                # QAction.triggered emits a bool 'checked'; swallow it to avoid type issues
                reset_action.triggered.connect(lambda checked=False, vn=view_name: self.on_menu_reset_view(vn))
                context_menu.addAction(reset_action)

            # Show menu at the correct position (cursor position)
            global_pos = button.mapToGlobal(pos)
            context_menu.exec_(global_pos)

            # Reset button state after menu closes
            QtCore.QTimer.singleShot(50, lambda: button.setAttribute(QtCore.Qt.WA_UnderMouse, False))

        except Exception as e:
            self.logger.error(f"Failed to show context menu for {view_name}: {e}")

    def import_image_for_view(self, view_name):
        """Import image for the specified view.

        为指定视图导入图片。

        Args:
            view_name: Name of the view ("正", "30°", "40°", "右")
        """
        try:
            # Open file dialog to select image
            file_dialog = QtWidgets.QFileDialog(self)
            file_dialog.setWindowTitle(f"选择{view_name}视图的参考图片")
            file_dialog.setFileMode(QtWidgets.QFileDialog.ExistingFile)
            file_dialog.setNameFilter("图片文件 (*.png *.jpg *.jpeg *.bmp *.tga *.tiff)")

            if file_dialog.exec_() == QtWidgets.QFileDialog.Accepted:
                selected_files = file_dialog.selectedFiles()
                if selected_files:
                    image_path = selected_files[0]

                    # Switch to the corresponding view first
                    ref_image_plane_utils.switch_view_by_name(view_name)

                    # Create image plane
                    plane_name = ref_image_plane_utils.create_image_plane(image_path, view_name)

                    if not plane_name:
                        # Show error message
                        QtWidgets.QMessageBox.warning(
                            self,
                            "导入失败",
                            f"无法为{view_name}视图创建图片平面",
                        )
                    else:
                        # Set button background image on successful import
                        self.set_button_background_image(view_name, image_path)

        except Exception as e:
            self.logger.error(f"Failed to import image for {view_name}: {e}")
            QtWidgets.QMessageBox.critical(
                self,
                "导入错误",
                f"导入图片时发生错误: {str(e)}",
            )

    def on_menu_horizontal_rotate_changed(self, view_name, value):
        """右键菜单-水平旋转滑杆改变。

        Args:
            view_name (str): "正" | "30°" | "40°" | "右"
            value (int): -15~15
        """
        try:
            # Apply horizontal rotation via utils
            ref_image_plane_utils.set_angle_view_horizontal_rotation(view_name, value)
        except Exception as e:
            self.logger.warning(f"水平旋转占位回调异常: view={view_name}, value={value}, err={e}")

    def on_menu_vertical_rotate_changed(self, view_name, value):
        """右键菜单-上下旋转滑杆改变。

        Args:
            view_name (str): "正" | "30°" | "40°" | "右"
            value (int): -15~15
        """
        try:
            # Apply vertical rotation via utils
            ref_image_plane_utils.set_angle_view_vertical_rotation(view_name, value)
        except Exception as e:
            self.logger.warning(f"上下旋转占位回调异常: view={view_name}, value={value}, err={e}")

    def on_menu_reset_view(self, view_name):
        """右键菜单-重置视角。"""
        try:
            # Ensure we are looking through the correct camera so user can see the effect
            current_view = ref_image_plane_utils.get_current_view_name()
            if current_view != view_name:
                ref_image_plane_utils.switch_view_by_name(view_name)
                QtCore.QTimer.singleShot(
                    120,
                    lambda: (
                        ref_image_plane_utils.reset_angle_view(view_name),
                        ref_image_plane_utils.focus_on_head_model(),
                    ),
                )
            else:
                ref_image_plane_utils.reset_angle_view(view_name)
                ref_image_plane_utils.focus_on_head_model()
        except Exception as e:
            self.logger.warning(f"重置视角回调异常: view={view_name}, err={e}")

    def delete_image_for_view(self, view_name):
        """Delete image plane for the specified view.

        删除指定视图的图片平面。

        Args:
            view_name: Name of the view ("正", "30°", "40°", "右")
        """
        try:
            success = ref_image_plane_utils.delete_image_plane_for_view(view_name)
            if success:
                # Reset button style after successful deletion
                self.reset_button_style(view_name)
        except Exception as delete_error:
            self.logger.error(f"Error during deletion: {delete_error}")
            QtWidgets.QMessageBox.warning(
                self,
                "删除失败",
                f"删除过程中发生错误: {str(delete_error)}",
            )

    def align_model_for_view(self, view_name):
        """Manually align camera to model for the specified view.

        手动对齐相机到指定视图的模型。

        Args:
            view_name (str): View name ("正", "30°", "40°", "右")
        """
        try:
            # First switch to the view if not already there
            current_view = ref_image_plane_utils.get_current_view_name()
            if current_view != view_name:
                ref_image_plane_utils.switch_view_by_name(view_name)
                # Wait a moment for view switch to complete
                QtCore.QTimer.singleShot(100, lambda: self._perform_alignment())
            else:
                self._perform_alignment()

        except Exception as e:
            self.logger.error(f"Failed to align model for {view_name}: {e}")
            QtWidgets.QMessageBox.critical(
                self,
                "对齐失败",
                f"对齐模型时发生错误: {str(e)}",
            )

    def _perform_alignment(self):
        """Perform the actual model alignment."""
        try:
            # Call the focus function directly
            ref_image_plane_utils.focus_on_head_model()
        except Exception as e:
            self.logger.error(f"Failed to perform alignment: {e}")
