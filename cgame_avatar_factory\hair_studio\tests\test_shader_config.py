"""Test module for shader configuration system."""

import unittest
from unittest.mock import patch, MagicMock
import os
import sys

# Add the project root to the path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from cgame_avatar_factory.hair_studio.config.shader_configs import (
    get_shader_config,
    register_shader_config,
    create_config_with_overrides,
    shader_registry,
    ASSET_SHADER_MAPPING,
)


class TestShaderConfig(unittest.TestCase):
    """Test cases for shader configuration system."""
    
    def test_get_default_configs(self):
        """Test getting default shader configurations."""
        # Test getting blinn config
        blinn_config = get_shader_config("blinn")
        self.assertEqual(blinn_config["shader_type"], "blinn")
        self.assertIn("color", blinn_config["texture_map"])
        
        # Test getting dx11_hair config
        hair_config = get_shader_config("dx11_hair")
        self.assertEqual(hair_config["shader_type"], "dx11Shader")
        self.assertIn("HairAlphaTexture", hair_config["texture_map"])
        self.assertIn("RootColor", hair_config["params"])
    
    def test_register_custom_config(self):
        """Test registering custom shader configuration."""
        custom_config = {
            "shader_type": "lambert",
            "texture_map": {"color": "_diffuse"},
            "params": {"transparency": 0.5},
            "fx_path": None,
        }
        
        register_shader_config("custom_lambert", custom_config)
        retrieved_config = get_shader_config("custom_lambert")
        
        self.assertEqual(retrieved_config["shader_type"], "lambert")
        self.assertEqual(retrieved_config["params"]["transparency"], 0.5)
    
    def test_config_with_overrides(self):
        """Test creating configuration with parameter overrides."""
        overrides = {
            "params": {
                "Roughness": 0.9,
                "NewParam": "test_value"
            }
        }
        
        new_config = create_config_with_overrides("dx11_hair", overrides)
        
        # Check that original params are preserved
        self.assertIn("RootColor", new_config["params"])
        # Check that overrides are applied
        self.assertEqual(new_config["params"]["Roughness"], 0.9)
        self.assertEqual(new_config["params"]["NewParam"], "test_value")
    
    def test_asset_shader_mapping(self):
        """Test asset to shader configuration mapping."""
        # Test simple mapping
        self.assertEqual(ASSET_SHADER_MAPPING["hair"], "dx11_hair")
        self.assertEqual(ASSET_SHADER_MAPPING["scalp"], "blinn")
        
        # Test complex mapping
        eyebrow_mapping = ASSET_SHADER_MAPPING["eyebrow"]
        self.assertIsInstance(eyebrow_mapping, dict)
        self.assertEqual(eyebrow_mapping["_Base"], "blinn")
        self.assertEqual(eyebrow_mapping["default"], "dx11_hair")
    
    def test_invalid_config_key(self):
        """Test handling of invalid configuration keys."""
        with self.assertRaises(KeyError):
            get_shader_config("nonexistent_config")
    
    def test_config_validation(self):
        """Test configuration validation."""
        # Test invalid config (missing required keys)
        invalid_config = {
            "shader_type": "blinn",
            # Missing texture_map and params
        }
        
        with self.assertRaises(ValueError):
            register_shader_config("invalid_config", invalid_config)


class TestShaderConfigIntegration(unittest.TestCase):
    """Integration tests for shader configuration with assign_shader module."""
    
    @patch('cgame_avatar_factory.hair_studio.maya_api.assign_shader.cmds')
    def test_shader_config_manager(self, mock_cmds):
        """Test ShaderConfigManager integration."""
        # Mock Maya commands
        mock_cmds.objExists.return_value = False
        mock_cmds.shadingNode.return_value = "test_shader"
        mock_cmds.attributeQuery.return_value = True
        mock_cmds.setAttr = MagicMock()
        
        from cgame_avatar_factory.hair_studio.maya_api.assign_shader import shader_config_manager
        
        # Test getting shader config
        config = shader_config_manager.load_shader_config("blinn")
        self.assertEqual(config["shader_type"], "blinn")
        
        # Test resolving asset shader config
        config_key = shader_config_manager.resolve_asset_shader_config("hair")
        self.assertEqual(config_key, "dx11_hair")
        
        # Test complex mapping resolution
        config_key = shader_config_manager.resolve_asset_shader_config("eyebrow", "test_Base_material")
        self.assertEqual(config_key, "blinn")


if __name__ == '__main__':
    unittest.main()
