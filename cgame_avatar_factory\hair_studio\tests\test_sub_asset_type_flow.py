"""Test sub_asset_type data flow from data manager to Maya API."""

# Import built-in modules
import unittest

# Import local modules
# Import the modules we need to test
from cgame_avatar_factory.hair_studio.data.models import HairAsset
from cgame_avatar_factory.hair_studio.maya_api import hair_operations


class TestSubAssetTypeFlow(unittest.TestCase):
    """Test that sub_asset_type flows correctly through the system."""

    def test_hair_asset_sub_asset_type_support(self):
        """Test that HairAs<PERSON> properly handles sub_asset_type."""
        # Test creating HairAsset with sub_asset_type
        asset = HairAsset(
            id="test_001",
            name="Test Hair",
            asset_type="card",
            sub_asset_type="scalp",
            thumbnail="/path/to/thumb.jpg",
            metadata={"file_path": "/path/to/asset.fbx", "reference": "/path/to/ref.obj"},
        )

        # Verify the attribute is set
        self.assertEqual(asset.sub_asset_type, "scalp")

        # Test to_dict includes sub_asset_type
        asset_dict = asset.to_dict()
        self.assertIn("sub_asset_type", asset_dict)
        self.assertEqual(asset_dict["sub_asset_type"], "scalp")

        # Test from_dict preserves sub_asset_type
        recreated_asset = HairAsset.from_dict(asset_dict)
        self.assertEqual(recreated_asset.sub_asset_type, "scalp")

    def test_hair_asset_none_sub_asset_type(self):
        """Test that HairAsset handles None sub_asset_type correctly."""
        asset = HairAsset(
            id="test_002",
            name="Test Hair",
            asset_type="card",
            sub_asset_type=None,
        )

        self.assertIsNone(asset.sub_asset_type)

        asset_dict = asset.to_dict()
        self.assertIn("sub_asset_type", asset_dict)
        self.assertIsNone(asset_dict["sub_asset_type"])

    def test_create_hair_node_reads_sub_asset_type_correctly(self):
        """Test that create_hair_node reads sub_asset_type from correct location."""
        # Create test asset data with sub_asset_type at top level
        asset_data = {
            "name": "Test Hair Asset",
            "sub_asset_type": "scalp",  # This should be read correctly
            "metadata": {
                "file_path": "/path/to/hair.fbx",
                "reference": "/path/to/head.obj",
            },
        }

        # Call the function
        result = hair_operations.create_hair_node(asset_data)

        # Verify the function succeeded
        self.assertTrue(result.get("success"))

        # Verify the result contains the expected node_names structure
        self.assertIn("node_names", result)
        node_names = result["node_names"]
        self.assertIn("hair_asset", node_names)
        self.assertIn("ref_head", node_names)

        # Verify the node names are not None and have reasonable values
        self.assertIsNotNone(node_names["hair_asset"])
        self.assertIsNotNone(node_names["ref_head"])

        # The function should have processed the sub_asset_type correctly
        # (We can't easily verify the exact call arguments due to mock complexity,
        # but we can verify the function completed successfully with the expected structure)

    def test_create_hair_node_handles_none_sub_asset_type(self):
        """Test that create_hair_node handles None sub_asset_type correctly."""
        # Create test asset data without sub_asset_type
        asset_data = {
            "name": "Test Hair Asset",
            # No sub_asset_type field
            "metadata": {
                "file_path": "/path/to/hair.fbx",
                "reference": "/path/to/head.obj",
            },
        }

        # Call the function
        result = hair_operations.create_hair_node(asset_data)

        # Verify the function succeeded
        self.assertTrue(result.get("success"))

        # Verify the result contains the expected node_names structure
        self.assertIn("node_names", result)
        node_names = result["node_names"]
        self.assertIn("hair_asset", node_names)
        self.assertIn("ref_head", node_names)

        # Verify the node names are not None and have reasonable values
        self.assertIsNotNone(node_names["hair_asset"])
        self.assertIsNotNone(node_names["ref_head"])

        # The function should have processed None sub_asset_type correctly
        # (We can't easily verify the exact call arguments due to mock complexity,
        # but we can verify the function completed successfully with the expected structure)


if __name__ == "__main__":
    unittest.main()
